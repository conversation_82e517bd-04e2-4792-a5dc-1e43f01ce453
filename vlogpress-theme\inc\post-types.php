<?php
/**
 * Custom post types for VlogPress theme
 *
 * @package VlogPress
 * @since 1.0.0
 */

/**
 * Register custom post type for vlogs
 */
function vlogpress_register_vlog_post_type() {
    $labels = array(
        'name'                  => _x( 'Vlogs', 'Post type general name', 'vlogpress' ),
        'singular_name'         => _x( 'Vlog', 'Post type singular name', 'vlogpress' ),
        'menu_name'             => _x( 'Vlogs', 'Admin Menu text', 'vlogpress' ),
        'name_admin_bar'        => _x( 'Vlog', 'Add New on Toolbar', 'vlogpress' ),
        'add_new'               => __( 'Add New', 'vlogpress' ),
        'add_new_item'          => __( 'Add New Vlog', 'vlogpress' ),
        'new_item'              => __( 'New Vlog', 'vlogpress' ),
        'edit_item'             => __( 'Edit Vlog', 'vlogpress' ),
        'view_item'             => __( 'View Vlog', 'vlogpress' ),
        'all_items'             => __( 'All Vlogs', 'vlogpress' ),
        'search_items'          => __( 'Search Vlogs', 'vlogpress' ),
        'parent_item_colon'     => __( 'Parent Vlogs:', 'vlogpress' ),
        'not_found'             => __( 'No vlogs found.', 'vlogpress' ),
        'not_found_in_trash'    => __( 'No vlogs found in Trash.', 'vlogpress' ),
        'featured_image'        => _x( 'Vlog Cover Image', 'Overrides the "Featured Image" phrase', 'vlogpress' ),
        'set_featured_image'    => _x( 'Set cover image', 'Overrides the "Set featured image" phrase', 'vlogpress' ),
        'remove_featured_image' => _x( 'Remove cover image', 'Overrides the "Remove featured image" phrase', 'vlogpress' ),
        'use_featured_image'    => _x( 'Use as cover image', 'Overrides the "Use as featured image" phrase', 'vlogpress' ),
        'archives'              => _x( 'Vlog archives', 'The post type archive label', 'vlogpress' ),
        'insert_into_item'      => _x( 'Insert into vlog', 'Overrides the "Insert into post" phrase', 'vlogpress' ),
        'uploaded_to_this_item' => _x( 'Uploaded to this vlog', 'Overrides the "Uploaded to this post" phrase', 'vlogpress' ),
        'filter_items_list'     => _x( 'Filter vlogs list', 'Screen reader text for the filter links', 'vlogpress' ),
        'items_list_navigation' => _x( 'Vlogs list navigation', 'Screen reader text for the pagination', 'vlogpress' ),
        'items_list'            => _x( 'Vlogs list', 'Screen reader text for the items list', 'vlogpress' ),
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array( 'slug' => 'vlog' ),
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => 5,
        'menu_icon'          => 'dashicons-video-alt3',
        'show_in_rest'       => true,
        'supports'           => array( 'title', 'editor', 'thumbnail', 'excerpt', 'comments', 'custom-fields' ),
        'taxonomies'         => array( 'category', 'post_tag' ),
    );

    register_post_type( 'vlog', $args );
}
add_action( 'init', 'vlogpress_register_vlog_post_type' );

/**
 * Add custom meta boxes for vlog post type
 */
function vlogpress_add_vlog_meta_boxes() {
    add_meta_box(
        'vlog-video-details',
        __( 'Video Details', 'vlogpress' ),
        'vlogpress_vlog_video_details_callback',
        'vlog',
        'normal',
        'high'
    );
}
add_action( 'add_meta_boxes', 'vlogpress_add_vlog_meta_boxes' );

/**
 * Meta box callback for video details
 */
function vlogpress_vlog_video_details_callback( $post ) {
    wp_nonce_field( 'vlogpress_save_vlog_meta', 'vlogpress_vlog_meta_nonce' );

    $video_url = get_post_meta( $post->ID, '_vp_video_url', true );
    $video_duration = get_post_meta( $post->ID, '_vp_video_duration', true );
    $view_count = get_post_meta( $post->ID, '_vp_view_count', true );

    ?>
    <table class="form-table">
        <tr>
            <th scope="row">
                <label for="vp_video_url"><?php _e( 'Video URL', 'vlogpress' ); ?></label>
            </th>
            <td>
                <input type="url" id="vp_video_url" name="vp_video_url" value="<?php echo esc_attr( $video_url ); ?>" class="regular-text" />
                <p class="description"><?php _e( 'Enter the video URL (YouTube, Vimeo, or self-hosted)', 'vlogpress' ); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="vp_video_duration"><?php _e( 'Video Duration', 'vlogpress' ); ?></label>
            </th>
            <td>
                <input type="text" id="vp_video_duration" name="vp_video_duration" value="<?php echo esc_attr( $video_duration ); ?>" class="small-text" />
                <p class="description"><?php _e( 'Enter duration in format: MM:SS or HH:MM:SS', 'vlogpress' ); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="vp_view_count"><?php _e( 'View Count', 'vlogpress' ); ?></label>
            </th>
            <td>
                <input type="number" id="vp_view_count" name="vp_view_count" value="<?php echo esc_attr( $view_count ); ?>" class="small-text" min="0" />
                <p class="description"><?php _e( 'Number of views (optional)', 'vlogpress' ); ?></p>
            </td>
        </tr>
    </table>
    <?php
}

/**
 * Save vlog meta data
 */
function vlogpress_save_vlog_meta( $post_id ) {
    if ( ! isset( $_POST['vlogpress_vlog_meta_nonce'] ) || ! wp_verify_nonce( $_POST['vlogpress_vlog_meta_nonce'], 'vlogpress_save_vlog_meta' ) ) {
        return;
    }

    if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
        return;
    }

    if ( ! current_user_can( 'edit_post', $post_id ) ) {
        return;
    }

    if ( isset( $_POST['vp_video_url'] ) ) {
        update_post_meta( $post_id, '_vp_video_url', sanitize_url( $_POST['vp_video_url'] ) );
    }

    if ( isset( $_POST['vp_video_duration'] ) ) {
        update_post_meta( $post_id, '_vp_video_duration', sanitize_text_field( $_POST['vp_video_duration'] ) );
    }

    if ( isset( $_POST['vp_view_count'] ) ) {
        update_post_meta( $post_id, '_vp_view_count', absint( $_POST['vp_view_count'] ) );
    }
}
add_action( 'save_post', 'vlogpress_save_vlog_meta' );