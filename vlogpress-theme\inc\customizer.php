<?php
/**
 * VlogPress Theme Customizer
 *
 * @package VlogPress
 * @since 1.0.0
 */

/**
 * Add postMessage support for site title and description for the Theme Customizer.
 *
 * @param WP_Customize_Manager $wp_customize Theme Customizer object.
 */
function vlogpress_customize_register( $wp_customize ) {
    $wp_customize->get_setting( 'blogname' )->transport         = 'postMessage';
    $wp_customize->get_setting( 'blogdescription' )->transport  = 'postMessage';
    $wp_customize->get_setting( 'header_textcolor' )->transport = 'postMessage';

    if ( isset( $wp_customize->selective_refresh ) ) {
        $wp_customize->selective_refresh->add_partial( 'blogname', array(
            'selector'        => '.site-title a',
            'render_callback' => 'vlogpress_customize_partial_blogname',
        ) );
        $wp_customize->selective_refresh->add_partial( 'blogdescription', array(
            'selector'        => '.site-description',
            'render_callback' => 'vlogpress_customize_partial_blogdescription',
        ) );
    }

    // Add VlogPress Theme Options Panel
    $wp_customize->add_panel( 'vlogpress_theme_options', array(
        'title'       => __( 'VlogPress Options', 'vlogpress' ),
        'description' => __( 'Customize your VlogPress theme settings.', 'vlogpress' ),
        'priority'    => 30,
    ) );

    // Colors Section
    $wp_customize->add_section( 'vlogpress_colors', array(
        'title'    => __( 'Theme Colors', 'vlogpress' ),
        'panel'    => 'vlogpress_theme_options',
        'priority' => 10,
    ) );

    // Primary Color
    $wp_customize->add_setting( 'vlogpress_primary_color', array(
        'default'           => '#2563eb',
        'sanitize_callback' => 'sanitize_hex_color',
        'transport'         => 'postMessage',
    ) );

    $wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'vlogpress_primary_color', array(
        'label'    => __( 'Primary Color', 'vlogpress' ),
        'section'  => 'vlogpress_colors',
        'settings' => 'vlogpress_primary_color',
    ) ) );

    // Accent Color
    $wp_customize->add_setting( 'vlogpress_accent_color', array(
        'default'           => '#dc2626',
        'sanitize_callback' => 'sanitize_hex_color',
        'transport'         => 'postMessage',
    ) );

    $wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, 'vlogpress_accent_color', array(
        'label'    => __( 'Accent Color', 'vlogpress' ),
        'section'  => 'vlogpress_colors',
        'settings' => 'vlogpress_accent_color',
    ) ) );

    // Layout Section
    $wp_customize->add_section( 'vlogpress_layout', array(
        'title'    => __( 'Layout Options', 'vlogpress' ),
        'panel'    => 'vlogpress_theme_options',
        'priority' => 20,
    ) );

    // Homepage Layout
    $wp_customize->add_setting( 'vlogpress_homepage_layout', array(
        'default'           => 'mixed',
        'sanitize_callback' => 'vlogpress_sanitize_select',
    ) );

    $wp_customize->add_control( 'vlogpress_homepage_layout', array(
        'label'    => __( 'Homepage Layout', 'vlogpress' ),
        'section'  => 'vlogpress_layout',
        'type'     => 'select',
        'choices'  => array(
            'mixed'      => __( 'Mixed Content (Videos + Blog Posts)', 'vlogpress' ),
            'videos'     => __( 'Videos Only', 'vlogpress' ),
            'blog'       => __( 'Blog Posts Only', 'vlogpress' ),
            'featured'   => __( 'Featured Content First', 'vlogpress' ),
        ),
    ) );

    // Content Grid Columns
    $wp_customize->add_setting( 'vlogpress_grid_columns', array(
        'default'           => '3',
        'sanitize_callback' => 'absint',
    ) );

    $wp_customize->add_control( 'vlogpress_grid_columns', array(
        'label'       => __( 'Content Grid Columns', 'vlogpress' ),
        'section'     => 'vlogpress_layout',
        'type'        => 'select',
        'choices'     => array(
            '2' => __( '2 Columns', 'vlogpress' ),
            '3' => __( '3 Columns', 'vlogpress' ),
            '4' => __( '4 Columns', 'vlogpress' ),
        ),
    ) );

    // Social Media Section
    $wp_customize->add_section( 'vlogpress_social', array(
        'title'    => __( 'Social Media Links', 'vlogpress' ),
        'panel'    => 'vlogpress_theme_options',
        'priority' => 30,
    ) );

    // Social Media Links
    $social_networks = array(
        'youtube'   => __( 'YouTube URL', 'vlogpress' ),
        'facebook'  => __( 'Facebook URL', 'vlogpress' ),
        'twitter'   => __( 'Twitter URL', 'vlogpress' ),
        'instagram' => __( 'Instagram URL', 'vlogpress' ),
        'linkedin'  => __( 'LinkedIn URL', 'vlogpress' ),
        'tiktok'    => __( 'TikTok URL', 'vlogpress' ),
    );

    foreach ( $social_networks as $network => $label ) {
        $wp_customize->add_setting( "vlogpress_social_{$network}", array(
            'default'           => '',
            'sanitize_callback' => 'esc_url_raw',
        ) );

        $wp_customize->add_control( "vlogpress_social_{$network}", array(
            'label'   => $label,
            'section' => 'vlogpress_social',
            'type'    => 'url',
        ) );
    }

    // Typography Section
    $wp_customize->add_section( 'vlogpress_typography', array(
        'title'    => __( 'Typography', 'vlogpress' ),
        'panel'    => 'vlogpress_theme_options',
        'priority' => 40,
    ) );

    // Body Font
    $wp_customize->add_setting( 'vlogpress_body_font', array(
        'default'           => 'system',
        'sanitize_callback' => 'vlogpress_sanitize_select',
    ) );

    $wp_customize->add_control( 'vlogpress_body_font', array(
        'label'   => __( 'Body Font', 'vlogpress' ),
        'section' => 'vlogpress_typography',
        'type'    => 'select',
        'choices' => array(
            'system'     => __( 'System Font', 'vlogpress' ),
            'inter'      => __( 'Inter', 'vlogpress' ),
            'roboto'     => __( 'Roboto', 'vlogpress' ),
            'open-sans'  => __( 'Open Sans', 'vlogpress' ),
            'lato'       => __( 'Lato', 'vlogpress' ),
        ),
    ) );

    // Heading Font
    $wp_customize->add_setting( 'vlogpress_heading_font', array(
        'default'           => 'system',
        'sanitize_callback' => 'vlogpress_sanitize_select',
    ) );

    $wp_customize->add_control( 'vlogpress_heading_font', array(
        'label'   => __( 'Heading Font', 'vlogpress' ),
        'section' => 'vlogpress_typography',
        'type'    => 'select',
        'choices' => array(
            'system'     => __( 'System Font', 'vlogpress' ),
            'inter'      => __( 'Inter', 'vlogpress' ),
            'roboto'     => __( 'Roboto', 'vlogpress' ),
            'open-sans'  => __( 'Open Sans', 'vlogpress' ),
            'lato'       => __( 'Lato', 'vlogpress' ),
            'playfair'   => __( 'Playfair Display', 'vlogpress' ),
            'merriweather' => __( 'Merriweather', 'vlogpress' ),
        ),
    ) );
}
add_action( 'customize_register', 'vlogpress_customize_register' );

/**
 * Render the site title for the selective refresh partial.
 *
 * @return void
 */
function vlogpress_customize_partial_blogname() {
    bloginfo( 'name' );
}

/**
 * Render the site tagline for the selective refresh partial.
 *
 * @return void
 */
function vlogpress_customize_partial_blogdescription() {
    bloginfo( 'description' );
}

/**
 * Sanitize select options
 */
function vlogpress_sanitize_select( $input, $setting ) {
    $input = sanitize_key( $input );
    $choices = $setting->manager->get_control( $setting->id )->choices;
    return ( array_key_exists( $input, $choices ) ? $input : $setting->default );
}

/**
 * Binds JS handlers to make Theme Customizer preview reload changes asynchronously.
 */
function vlogpress_customize_preview_js() {
    wp_enqueue_script( 'vlogpress-customizer', get_template_directory_uri() . '/js/customizer.js', array( 'customize-preview' ), VLOGPRESS_VERSION, true );
}
add_action( 'customize_preview_init', 'vlogpress_customize_preview_js' );

/**
 * Output customizer styles
 */
function vlogpress_customizer_css() {
    $primary_color = get_theme_mod( 'vlogpress_primary_color', '#2563eb' );
    $accent_color = get_theme_mod( 'vlogpress_accent_color', '#dc2626' );

    ?>
    <style type="text/css">
        :root {
            --vp-primary: <?php echo esc_attr( $primary_color ); ?>;
            --vp-accent: <?php echo esc_attr( $accent_color ); ?>;
        }
    </style>
    <?php
}
add_action( 'wp_head', 'vlogpress_customizer_css' );