<?php
/**
 * The main template file
 *
 * This is the most generic template file in a WordPress theme
 * and one of the two required files for a theme (the other being style.css).
 * It is used to display a page when nothing more specific matches a query.
 *
 * @package VlogPress
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main" role="main">
    <div class="vp-container">

        <?php if ( is_home() && ! is_front_page() ) : ?>
            <header class="page-header">
                <h1 class="page-title"><?php single_post_title(); ?></h1>
            </header>
        <?php endif; ?>

        <?php if ( have_posts() ) : ?>

            <div class="vp-content-grid">
                <?php while ( have_posts() ) : the_post(); ?>

                    <article id="post-<?php the_ID(); ?>" <?php post_class( 'vp-content-card' ); ?>>

                        <?php if ( has_post_thumbnail() ) : ?>
                            <div class="vp-card-thumbnail">
                                <a href="<?php the_permalink(); ?>" aria-hidden="true" tabindex="-1">
                                    <?php the_post_thumbnail( 'medium_large', array( 'alt' => the_title_attribute( array( 'echo' => false ) ) ) ); ?>
                                </a>

                                <?php if ( get_post_type() === 'vlog' ) : ?>
                                    <div class="vp-video-overlay">
                                        <svg class="vp-play-icon" width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M8 5V19L19 12L8 5Z" fill="currentColor"/>
                                        </svg>
                                    </div>

                                    <?php
                                    $video_duration = get_post_meta( get_the_ID(), '_vp_video_duration', true );
                                    if ( $video_duration ) : ?>
                                        <div class="vp-video-duration"><?php echo esc_html( $video_duration ); ?></div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <div class="vp-card-content">
                            <header class="vp-card-header">
                                <?php
                                if ( is_singular() ) :
                                    the_title( '<h1 class="vp-card-title">', '</h1>' );
                                else :
                                    the_title( '<h2 class="vp-card-title"><a href="' . esc_url( get_permalink() ) . '" rel="bookmark">', '</a></h2>' );
                                endif;
                                ?>

                                <div class="vp-card-meta">
                                    <span class="vp-content-type <?php echo get_post_type() === 'vlog' ? 'vp-type-video' : 'vp-type-blog'; ?>">
                                        <?php echo get_post_type() === 'vlog' ? __( 'Video', 'vlogpress' ) : __( 'Blog', 'vlogpress' ); ?>
                                    </span>

                                    <time class="vp-published-date" datetime="<?php echo esc_attr( get_the_date( 'c' ) ); ?>">
                                        <?php echo esc_html( get_the_date() ); ?>
                                    </time>

                                    <?php if ( get_post_type() === 'post' ) : ?>
                                        <span class="vp-reading-time">
                                            <?php echo vp_get_reading_time(); ?>
                                        </span>
                                    <?php endif; ?>

                                    <?php if ( get_post_type() === 'vlog' ) : ?>
                                        <?php
                                        $view_count = get_post_meta( get_the_ID(), '_vp_view_count', true );
                                        if ( $view_count ) : ?>
                                            <span class="vp-view-count">
                                                <?php printf( _n( '%s view', '%s views', $view_count, 'vlogpress' ), number_format_i18n( $view_count ) ); ?>
                                            </span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </header>

                            <div class="vp-card-excerpt">
                                <?php the_excerpt(); ?>
                            </div>

                            <footer class="vp-card-footer">
                                <a href="<?php the_permalink(); ?>" class="vp-read-more">
                                    <?php echo get_post_type() === 'vlog' ? __( 'Watch Video', 'vlogpress' ) : __( 'Read More', 'vlogpress' ); ?>
                                    <span class="vp-sr-only"><?php printf( __( 'about %s', 'vlogpress' ), get_the_title() ); ?></span>
                                </a>

                                <?php if ( has_category() || has_tag() ) : ?>
                                    <div class="vp-card-terms">
                                        <?php
                                        $categories = get_the_category();
                                        if ( $categories ) {
                                            echo '<span class="vp-category">' . esc_html( $categories[0]->name ) . '</span>';
                                        }
                                        ?>
                                    </div>
                                <?php endif; ?>
                            </footer>
                        </div>
                    </article>

                <?php endwhile; ?>
            </div>

            <?php
            // Pagination
            the_posts_pagination( array(
                'mid_size'  => 2,
                'prev_text' => __( '&larr; Previous', 'vlogpress' ),
                'next_text' => __( 'Next &rarr;', 'vlogpress' ),
                'class'     => 'vp-pagination',
            ) );
            ?>

        <?php else : ?>

            <section class="vp-no-results">
                <header class="page-header">
                    <h1 class="page-title"><?php _e( 'Nothing here', 'vlogpress' ); ?></h1>
                </header>

                <div class="page-content">
                    <?php if ( is_home() && current_user_can( 'publish_posts' ) ) : ?>
                        <p>
                            <?php
                            printf(
                                wp_kses(
                                    __( 'Ready to publish your first post? <a href="%1$s">Get started here</a>.', 'vlogpress' ),
                                    array(
                                        'a' => array(
                                            'href' => array(),
                                        ),
                                    )
                                ),
                                esc_url( admin_url( 'post-new.php' ) )
                            );
                            ?>
                        </p>
                    <?php elseif ( is_search() ) : ?>
                        <p><?php _e( 'Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'vlogpress' ); ?></p>
                        <?php get_search_form(); ?>
                    <?php else : ?>
                        <p><?php _e( 'It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help.', 'vlogpress' ); ?></p>
                        <?php get_search_form(); ?>
                    <?php endif; ?>
                </div>
            </section>

        <?php endif; ?>

    </div>
</main>

<?php
get_sidebar();
get_footer();