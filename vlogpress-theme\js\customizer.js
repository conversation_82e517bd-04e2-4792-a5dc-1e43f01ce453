/**
 * VlogPress Theme Customizer JS
 *
 * @package VlogPress
 * @since 1.0.0
 */

(function( $ ) {
    'use strict';

    // Site title and description
    wp.customize( 'blogname', function( value ) {
        value.bind( function( to ) {
            $( '.site-title a' ).text( to );
        } );
    } );

    wp.customize( 'blogdescription', function( value ) {
        value.bind( function( to ) {
            $( '.site-description' ).text( to );
        } );
    } );

    // Header text color
    wp.customize( 'header_textcolor', function( value ) {
        value.bind( function( to ) {
            if ( 'blank' === to ) {
                $( '.site-title, .site-description' ).css( {
                    'clip': 'rect(1px, 1px, 1px, 1px)',
                    'position': 'absolute'
                } );
            } else {
                $( '.site-title, .site-description' ).css( {
                    'clip': 'auto',
                    'position': 'relative'
                } );
                $( '.site-title a, .site-description' ).css( {
                    'color': to
                } );
            }
        } );
    } );

    // Primary color
    wp.customize( 'vlogpress_primary_color', function( value ) {
        value.bind( function( to ) {
            $( 'head' ).find( '#vlogpress-primary-color' ).remove();
            $( 'head' ).append( '<style id="vlogpress-primary-color">:root { --vp-primary: ' + to + '; }</style>' );
        } );
    } );

    // Accent color
    wp.customize( 'vlogpress_accent_color', function( value ) {
        value.bind( function( to ) {
            $( 'head' ).find( '#vlogpress-accent-color' ).remove();
            $( 'head' ).append( '<style id="vlogpress-accent-color">:root { --vp-accent: ' + to + '; }</style>' );
        } );
    } );

})( jQuery );