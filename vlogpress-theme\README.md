# VlogPress - Hybrid Video & Blog WordPress Theme

A modern, responsive WordPress theme that seamlessly combines video blogging (vlogging) and traditional text blogging capabilities. Perfect for content creators, influencers, and businesses who create both video and written content.

## 🎯 Features

### Core Functionality
- **Hybrid Content System**: Seamlessly mix video vlogs and blog posts
- **Custom Video Post Type**: Dedicated 'vlog' post type with video-specific fields
- **Responsive Video Players**: Support for YouTube, Vimeo, and self-hosted videos
- **Unified Taxonomy**: Categories and tags work across both content types
- **Cross-Content Discovery**: Smart recommendations between videos and blog posts

### Video Features
- **Multiple Video Sources**: YouTube, Vimeo, self-hosted (MP4, WebM)
- **Video Metadata**: Duration, view count, custom thumbnails
- **Responsive Video Containers**: 16:9 aspect ratio with mobile optimization
- **Video Overlay Effects**: Play button overlays and duration badges
- **Lazy Loading**: Performance-optimized video loading

### Design & UX
- **Mobile-First Design**: Optimized for all devices and screen sizes
- **Modern CSS Framework**: CSS custom properties and utility classes
- **Accessibility Compliant**: WCAG 2.1 AA standards
- **Clean Typography**: Optimized for readability
- **Smooth Animations**: CSS transitions and hover effects

### WordPress Integration
- **Gutenberg Compatible**: Full block editor support
- **Custom Menus**: Primary, footer, and social link menus
- **Widget Areas**: Sidebar and footer widget zones
- **Theme Customizer**: Live preview customization options
- **SEO Optimized**: Structured data and meta tags

## 📁 File Structure

```
vlogpress-theme/
├── assets/
│   ├── fonts/
│   └── icons/
├── css/
│   ├── components/
│   │   └── video.css
│   ├── layouts/
│   └── utilities/
├── js/
│   ├── components/
│   ├── modules/
│   └── theme.js
├── inc/
│   ├── customizer/
│   ├── post-types/
│   ├── widgets/
│   ├── customizer.php
│   ├── post-types.php
│   ├── template-tags.php
│   ├── template-functions.php
│   ├── custom-header.php
│   └── jetpack.php
├── template-parts/
│   ├── content/
│   ├── navigation/
│   └── sidebar/
├── languages/
├── images/
├── style.css
├── index.php
├── functions.php
├── header.php
├── footer.php
├── sidebar.php
├── single.php
├── archive.php
├── page.php
└── README.md
```

## 🚀 Installation

1. **Download the theme files**
2. **Upload to WordPress**:
   - Via Admin: `Appearance > Themes > Add New > Upload Theme`
   - Via FTP: Upload to `/wp-content/themes/vlogpress-theme/`
3. **Activate the theme**: `Appearance > Themes > VlogPress > Activate`

## ⚙️ Configuration

### Custom Post Type: Vlogs
The theme automatically registers a 'vlog' custom post type with:
- Video URL field (YouTube, Vimeo, or self-hosted)
- Video duration field
- View count field
- Standard WordPress features (title, content, thumbnail, categories, tags)

### Video Setup
1. **Create a new Vlog post**: `Vlogs > Add New`
2. **Add video details**:
   - **Video URL**: Enter YouTube, Vimeo, or self-hosted video URL
   - **Duration**: Format as MM:SS or HH:MM:SS
   - **View Count**: Optional view counter
3. **Set featured image**: Used as video thumbnail
4. **Add content**: Video description and additional content

### Menu Setup
1. **Create menus**: `Appearance > Menus`
2. **Assign to locations**:
   - **Primary Menu**: Main navigation
   - **Footer Menu**: Footer links
   - **Social Links Menu**: Social media icons

### Widget Areas
- **Primary Sidebar**: Main sidebar content
- **Footer Widget Area 1-3**: Three-column footer widgets

## 🎨 Customization

### CSS Custom Properties
The theme uses CSS custom properties for easy customization:

```css
:root {
    --vp-primary: #2563eb;
    --vp-primary-dark: #1e40af;
    --vp-text-primary: #1f2937;
    --vp-font-size-base: 1rem;
    --vp-space-md: 1rem;
}
```

### Theme Customizer
Access via `Appearance > Customize`:
- Site identity (logo, title, tagline)
- Colors and typography
- Header and footer options
- Widget management

### Child Theme Support
For custom modifications, create a child theme:

```php
// child-theme/style.css
/*
Theme Name: VlogPress Child
Template: vlogpress-theme
*/

@import url("../vlogpress-theme/style.css");

/* Your custom styles here */
```

## 🔧 Development

### CSS Architecture
- **Base styles**: Reset, typography, utilities
- **Components**: Reusable UI components
- **Layouts**: Page-specific layouts
- **Utilities**: Helper classes

### JavaScript Features
- Mobile menu toggle
- Search functionality
- Smooth scrolling
- Video lazy loading
- Responsive behavior

### PHP Structure
- **Template hierarchy**: Standard WordPress templates
- **Custom functions**: Theme-specific functionality
- **Hook system**: WordPress actions and filters
- **Security**: Proper sanitization and validation

## 🌐 Browser Support

- **Chrome**: Latest 2 versions
- **Firefox**: Latest 2 versions
- **Safari**: Latest 2 versions
- **Edge**: Latest 2 versions
- **Mobile browsers**: iOS Safari, Chrome Mobile

## 📱 Responsive Breakpoints

```css
/* Mobile */
@media (max-width: 767px) { }

/* Tablet */
@media (min-width: 768px) and (max-width: 1023px) { }

/* Desktop */
@media (min-width: 1024px) { }

/* Large Desktop */
@media (min-width: 1440px) { }
```

## 🔌 Plugin Compatibility

### Recommended Plugins
- **Yoast SEO**: Enhanced SEO features
- **WooCommerce**: E-commerce integration
- **Contact Form 7**: Contact forms
- **Jetpack**: Additional features and performance
- **Advanced Custom Fields**: Extended custom fields

### Tested Plugins
- Elementor Page Builder
- WPBakery Page Builder
- Mailchimp for WordPress
- Social media plugins
- Caching plugins

## 🎯 Performance Features

- **Lazy loading**: Images and videos
- **Minified assets**: Compressed CSS and JavaScript
- **Optimized queries**: Efficient database operations
- **CDN ready**: Static asset optimization
- **Caching support**: Compatible with caching plugins

## ♿ Accessibility Features

- **WCAG 2.1 AA compliant**
- **Keyboard navigation**: Full keyboard support
- **Screen reader friendly**: Proper ARIA labels
- **High contrast support**: Accessible color schemes
- **Focus indicators**: Clear focus states
- **Semantic HTML**: Proper heading hierarchy

## 🐛 Troubleshooting

### Common Issues

**Videos not displaying:**
- Check video URL format
- Ensure oEmbed is enabled
- Verify video privacy settings

**Layout issues:**
- Clear caching plugins
- Check for plugin conflicts
- Verify theme files are complete

**Performance issues:**
- Enable caching
- Optimize images
- Use CDN for video content

## 📄 License

This theme is licensed under the GPL v2 or later.

## 🤝 Support

For support and documentation:
- **Theme Documentation**: [Link to docs]
- **Support Forum**: [Link to forum]
- **GitHub Issues**: [Link to GitHub]

## 🔄 Changelog

### Version 1.0.0
- Initial release
- Hybrid video/blog functionality
- Responsive design
- Accessibility compliance
- Performance optimization

---

**VlogPress Theme** - Bridging the gap between video and written content for modern content creators.