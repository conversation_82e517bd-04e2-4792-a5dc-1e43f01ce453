<?php
/**
 * The header for our theme
 *
 * This is the template that displays all of the <head> section and everything up until <div id="content">
 *
 * @package VlogPress
 * @since 1.0.0
 */

?><!doctype html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo( 'charset' ); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">

    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<div id="page" class="site">
    <a class="skip-link vp-sr-only" href="#main"><?php _e( 'Skip to content', 'vlogpress' ); ?></a>

    <header id="masthead" class="site-header" role="banner">
        <div class="vp-container">
            <div class="site-branding">
                <?php
                if ( has_custom_logo() ) :
                    the_custom_logo();
                else :
                    ?>
                    <h1 class="site-title">
                        <a href="<?php echo esc_url( home_url( '/' ) ); ?>" rel="home">
                            <?php bloginfo( 'name' ); ?>
                        </a>
                    </h1>
                    <?php
                    $description = get_bloginfo( 'description', 'display' );
                    if ( $description || is_customize_preview() ) :
                        ?>
                        <p class="site-description"><?php echo $description; ?></p>
                    <?php endif;
                endif;
                ?>
            </div><!-- .site-branding -->

            <nav id="site-navigation" class="main-navigation" role="navigation" aria-label="<?php esc_attr_e( 'Primary Menu', 'vlogpress' ); ?>">
                <button class="menu-toggle" aria-controls="primary-menu" aria-expanded="false">
                    <span class="vp-sr-only"><?php _e( 'Primary Menu', 'vlogpress' ); ?></span>
                    <span class="menu-toggle-icon">
                        <span></span>
                        <span></span>
                        <span></span>
                    </span>
                </button>

                <?php
                wp_nav_menu( array(
                    'theme_location' => 'primary',
                    'menu_id'        => 'primary-menu',
                    'menu_class'     => 'primary-menu',
                    'container'      => false,
                    'fallback_cb'    => 'vlogpress_default_menu',
                ) );
                ?>
            </nav><!-- #site-navigation -->

            <div class="header-actions">
                <div class="search-toggle">
                    <button class="search-toggle-button" aria-controls="search-form" aria-expanded="false">
                        <span class="vp-sr-only"><?php _e( 'Search', 'vlogpress' ); ?></span>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>

                    <div class="search-form-container" id="search-form">
                        <?php get_search_form(); ?>
                    </div>
                </div>

                <?php if ( has_nav_menu( 'social' ) ) : ?>
                    <nav class="social-navigation" role="navigation" aria-label="<?php esc_attr_e( 'Social Links Menu', 'vlogpress' ); ?>">
                        <?php
                        wp_nav_menu( array(
                            'theme_location' => 'social',
                            'menu_class'     => 'social-links-menu',
                            'container'      => false,
                            'depth'          => 1,
                            'link_before'    => '<span class="vp-sr-only">',
                            'link_after'     => '</span>',
                        ) );
                        ?>
                    </nav><!-- .social-navigation -->
                <?php endif; ?>
            </div><!-- .header-actions -->
        </div><!-- .vp-container -->
    </header><!-- #masthead -->

    <div id="content" class="site-content"><?php
/**
 * Default menu fallback
 */
function vlogpress_default_menu() {
    echo '<ul id="primary-menu" class="primary-menu">';
    echo '<li><a href="' . esc_url( home_url( '/' ) ) . '">' . __( 'Home', 'vlogpress' ) . '</a></li>';

    if ( get_option( 'show_on_front' ) === 'page' ) {
        $blog_page_id = get_option( 'page_for_posts' );
        if ( $blog_page_id ) {
            echo '<li><a href="' . esc_url( get_permalink( $blog_page_id ) ) . '">' . __( 'Blog', 'vlogpress' ) . '</a></li>';
        }
    }

    // Add sample menu items
    echo '<li><a href="#">' . __( 'Videos', 'vlogpress' ) . '</a></li>';
    echo '<li><a href="#">' . __( 'About', 'vlogpress' ) . '</a></li>';
    echo '<li><a href="#">' . __( 'Contact', 'vlogpress' ) . '</a></li>';
    echo '</ul>';
}