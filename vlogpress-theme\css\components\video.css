/* ==========================================================================
   Video Components
   ========================================================================== */

/* Video Container */
.video-content {
    position: relative;
    margin-bottom: var(--vp-space-lg);
}

.video-content iframe,
.video-content video {
    width: 100%;
    height: auto;
    border-radius: var(--vp-radius-lg);
}

/* Responsive Video Wrapper */
.vp-video-wrapper {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
    border-radius: var(--vp-radius-lg);
    background-color: var(--vp-background-alt);
}

.vp-video-wrapper iframe,
.vp-video-wrapper video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
}

/* Video Overlay for Thumbnails */
.vp-video-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    padding: var(--vp-space-md);
    transition: all var(--vp-transition-normal);
    z-index: 2;
}

.vp-video-overlay:hover {
    background-color: rgba(0, 0, 0, 0.9);
    transform: translate(-50%, -50%) scale(1.1);
}

.vp-play-icon {
    color: white;
    width: 48px;
    height: 48px;
}

/* Video Duration Badge */
.vp-video-duration {
    position: absolute;
    bottom: var(--vp-space-sm);
    right: var(--vp-space-sm);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: var(--vp-space-xs) var(--vp-space-sm);
    border-radius: var(--vp-radius-sm);
    font-size: var(--vp-font-size-sm);
    font-weight: 600;
    z-index: 3;
}

/* Video Card Styles */
.vp-content-card.post-type-vlog .vp-card-thumbnail {
    position: relative;
    overflow: hidden;
    border-radius: var(--vp-radius-lg);
}

.vp-content-card.post-type-vlog .vp-card-thumbnail::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(220, 38, 38, 0.1) 100%);
    z-index: 1;
}

/* Video Meta Information */
.vp-video-meta {
    display: flex;
    align-items: center;
    gap: var(--vp-space-md);
    margin-bottom: var(--vp-space-md);
    padding: var(--vp-space-md);
    background-color: var(--vp-background-alt);
    border-radius: var(--vp-radius-md);
}

.vp-video-meta .vp-meta-item {
    display: flex;
    align-items: center;
    gap: var(--vp-space-xs);
    font-size: var(--vp-font-size-sm);
    color: var(--vp-text-secondary);
}

.vp-video-meta .vp-meta-icon {
    width: 16px;
    height: 16px;
}

/* Video Playlist Styles */
.vp-video-playlist {
    background-color: var(--vp-background-alt);
    border-radius: var(--vp-radius-lg);
    padding: var(--vp-space-lg);
    margin-bottom: var(--vp-space-xl);
}

.vp-playlist-title {
    font-size: var(--vp-font-size-lg);
    font-weight: 600;
    margin-bottom: var(--vp-space-md);
    color: var(--vp-text-primary);
}

.vp-playlist-item {
    display: flex;
    align-items: center;
    gap: var(--vp-space-md);
    padding: var(--vp-space-sm);
    border-radius: var(--vp-radius-md);
    transition: background-color var(--vp-transition-fast);
    text-decoration: none;
    color: inherit;
}

.vp-playlist-item:hover {
    background-color: var(--vp-background);
}

.vp-playlist-item.current {
    background-color: var(--vp-primary-light);
}

.vp-playlist-thumbnail {
    width: 80px;
    height: 45px;
    border-radius: var(--vp-radius-sm);
    overflow: hidden;
    flex-shrink: 0;
    position: relative;
}

.vp-playlist-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.vp-playlist-info {
    flex: 1;
    min-width: 0;
}

.vp-playlist-item-title {
    font-size: var(--vp-font-size-sm);
    font-weight: 500;
    margin-bottom: var(--vp-space-xs);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.vp-playlist-item-duration {
    font-size: var(--vp-font-size-xs);
    color: var(--vp-text-secondary);
}

/* Video Loading States */
.vp-video-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    background-color: var(--vp-background-alt);
    border-radius: var(--vp-radius-lg);
}

.vp-video-loading::after {
    content: '';
    width: 40px;
    height: 40px;
    border: 3px solid var(--vp-border);
    border-top: 3px solid var(--vp-primary);
    border-radius: 50%;
    animation: vp-spin 1s linear infinite;
}

@keyframes vp-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .vp-video-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--vp-space-sm);
    }

    .vp-playlist-item {
        flex-direction: column;
        text-align: center;
    }

    .vp-playlist-thumbnail {
        width: 120px;
        height: 68px;
    }

    .vp-play-icon {
        width: 32px;
        height: 32px;
    }
}