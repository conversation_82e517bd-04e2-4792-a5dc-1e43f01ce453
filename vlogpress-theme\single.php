<?php
/**
 * The template for displaying all single posts
 *
 * @package VlogPress
 * @since 1.0.0
 */

get_header();
?>

<main id="main" class="site-main" role="main">
    <div class="vp-container">

        <?php while ( have_posts() ) : the_post(); ?>

            <article id="post-<?php the_ID(); ?>" <?php post_class( 'vp-single-post' ); ?>>

                <header class="entry-header">
                    <?php
                    if ( 'post' === get_post_type() ) :
                        ?>
                        <div class="entry-meta">
                            <span class="content-type vp-type-blog"><?php _e( 'Blog Post', 'vlogpress' ); ?></span>

                            <time class="published-date" datetime="<?php echo esc_attr( get_the_date( 'c' ) ); ?>">
                                <?php echo esc_html( get_the_date() ); ?>
                            </time>

                            <span class="reading-time"><?php echo vp_get_reading_time(); ?></span>

                            <?php if ( has_category() ) : ?>
                                <div class="post-categories">
                                    <?php the_category( ', ' ); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <?php
                    elseif ( 'vlog' === get_post_type() ) :
                        ?>
                        <div class="entry-meta">
                            <span class="content-type vp-type-video"><?php _e( 'Video', 'vlogpress' ); ?></span>

                            <time class="published-date" datetime="<?php echo esc_attr( get_the_date( 'c' ) ); ?>">
                                <?php echo esc_html( get_the_date() ); ?>
                            </time>

                            <?php
                            $video_duration = get_post_meta( get_the_ID(), '_vp_video_duration', true );
                            if ( $video_duration ) : ?>
                                <span class="video-duration"><?php echo esc_html( $video_duration ); ?></span>
                            <?php endif; ?>

                            <?php
                            $view_count = get_post_meta( get_the_ID(), '_vp_view_count', true );
                            if ( $view_count ) : ?>
                                <span class="view-count">
                                    <?php printf( _n( '%s view', '%s views', $view_count, 'vlogpress' ), number_format_i18n( $view_count ) ); ?>
                                </span>
                            <?php endif; ?>
                        </div>
                        <?php
                    endif;

                    the_title( '<h1 class="entry-title">', '</h1>' );
                    ?>
                </header><!-- .entry-header -->

                <?php if ( 'vlog' === get_post_type() ) : ?>
                    <div class="video-content">
                        <?php
                        $video_url = get_post_meta( get_the_ID(), '_vp_video_url', true );
                        if ( $video_url ) :
                            // Handle different video sources
                            if ( strpos( $video_url, 'youtube.com' ) !== false || strpos( $video_url, 'youtu.be' ) !== false ) :
                                echo wp_oembed_get( $video_url );
                            elseif ( strpos( $video_url, 'vimeo.com' ) !== false ) :
                                echo wp_oembed_get( $video_url );
                            else :
                                // Self-hosted video
                                echo do_shortcode( '[video src="' . esc_url( $video_url ) . '"]' );
                            endif;
                        elseif ( has_post_thumbnail() ) :
                            the_post_thumbnail( 'large' );
                        endif;
                        ?>
                    </div><!-- .video-content -->
                <?php elseif ( has_post_thumbnail() ) : ?>
                    <div class="post-thumbnail">
                        <?php the_post_thumbnail( 'large' ); ?>
                    </div>
                <?php endif; ?>

                <div class="entry-content">
                    <?php
                    the_content( sprintf(
                        wp_kses(
                            __( 'Continue reading<span class="vp-sr-only"> "%s"</span>', 'vlogpress' ),
                            array(
                                'span' => array(
                                    'class' => array(),
                                ),
                            )
                        ),
                        get_the_title()
                    ) );

                    wp_link_pages( array(
                        'before' => '<div class="page-links">' . esc_html__( 'Pages:', 'vlogpress' ),
                        'after'  => '</div>',
                    ) );
                    ?>
                </div><!-- .entry-content -->

                <footer class="entry-footer">
                    <?php if ( has_tag() ) : ?>
                        <div class="post-tags">
                            <span class="tags-label"><?php _e( 'Tags:', 'vlogpress' ); ?></span>
                            <?php the_tags( '', ', ', '' ); ?>
                        </div>
                    <?php endif; ?>

                    <div class="post-actions">
                        <div class="social-share">
                            <span class="share-label"><?php _e( 'Share:', 'vlogpress' ); ?></span>
                            <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode( get_permalink() ); ?>&text=<?php echo urlencode( get_the_title() ); ?>" target="_blank" rel="noopener" class="share-twitter">
                                <span class="vp-sr-only"><?php _e( 'Share on Twitter', 'vlogpress' ); ?></span>
                                Twitter
                            </a>
                            <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode( get_permalink() ); ?>" target="_blank" rel="noopener" class="share-facebook">
                                <span class="vp-sr-only"><?php _e( 'Share on Facebook', 'vlogpress' ); ?></span>
                                Facebook
                            </a>
                            <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode( get_permalink() ); ?>" target="_blank" rel="noopener" class="share-linkedin">
                                <span class="vp-sr-only"><?php _e( 'Share on LinkedIn', 'vlogpress' ); ?></span>
                                LinkedIn
                            </a>
                        </div>
                    </div>
                </footer><!-- .entry-footer -->

            </article><!-- #post-<?php the_ID(); ?> -->

            <?php
            // Post navigation
            the_post_navigation( array(
                'prev_text' => '<span class="nav-subtitle">' . esc_html__( 'Previous:', 'vlogpress' ) . '</span> <span class="nav-title">%title</span>',
                'next_text' => '<span class="nav-subtitle">' . esc_html__( 'Next:', 'vlogpress' ) . '</span> <span class="nav-title">%title</span>',
            ) );

            // If comments are open or we have at least one comment, load up the comment template.
            if ( comments_open() || get_comments_number() ) :
                comments_template();
            endif;
            ?>

        <?php endwhile; ?>

    </div>
</main>

<?php
get_sidebar();
get_footer();