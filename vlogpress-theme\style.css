/*
Theme Name: VlogPress
Description: A modern, responsive WordPress theme that seamlessly combines video blogging (vlogging) and traditional text blogging capabilities. Perfect for content creators, influencers, and businesses who create both video and written content.
Author: VlogPress Team
Version: 1.0.0
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: vlogpress
Tags: blog, video, vlog, responsive, custom-post-types, featured-images, threaded-comments, translation-ready, accessibility-ready
*/

/* ==========================================================================
   CSS Reset & Base Styles
   ========================================================================== */

*,
*::before,
*::after {
    box-sizing: border-box;
}

html {
    font-size: 16px;
    line-height: 1.6;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    font-size: 1rem;
    line-height: 1.6;
    color: #1f2937;
    background-color: #ffffff;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ==========================================================================
   CSS Custom Properties (Variables)
   ========================================================================== */

:root {
    /* Colors */
    --vp-primary: #2563eb;
    --vp-primary-dark: #1e40af;
    --vp-primary-light: #dbeafe;
    --vp-text-primary: #1f2937;
    --vp-text-secondary: #6b7280;
    --vp-text-light: #9ca3af;
    --vp-background: #ffffff;
    --vp-background-alt: #f3f4f6;
    --vp-border: #e5e7eb;
    --vp-success: #10b981;
    --vp-warning: #f59e0b;
    --vp-error: #ef4444;
    --vp-video: #dc2626;

    /* Typography */
    --vp-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    --vp-font-size-xs: 0.75rem;
    --vp-font-size-sm: 0.875rem;
    --vp-font-size-base: 1rem;
    --vp-font-size-lg: 1.125rem;
    --vp-font-size-xl: 1.25rem;
    --vp-font-size-2xl: 1.5rem;
    --vp-font-size-3xl: 2rem;
    --vp-font-size-4xl: 2.5rem;

    /* Line Heights */
    --vp-line-height-tight: 1.2;
    --vp-line-height-normal: 1.6;
    --vp-line-height-relaxed: 1.8;

    /* Spacing */
    --vp-space-xs: 0.25rem;
    --vp-space-sm: 0.5rem;
    --vp-space-md: 1rem;
    --vp-space-lg: 1.5rem;
    --vp-space-xl: 2rem;
    --vp-space-2xl: 3rem;
    --vp-space-3xl: 4rem;

    /* Container */
    --vp-container-sm: 640px;
    --vp-container-md: 768px;
    --vp-container-lg: 1024px;
    --vp-container-xl: 1280px;
    --vp-container-2xl: 1400px;

    /* Transitions */
    --vp-transition-fast: 0.15s ease;
    --vp-transition-normal: 0.2s ease;
    --vp-transition-slow: 0.3s ease;

    /* Shadows */
    --vp-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --vp-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --vp-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* Border Radius */
    --vp-radius-sm: 0.25rem;
    --vp-radius-md: 0.375rem;
    --vp-radius-lg: 0.5rem;
    --vp-radius-xl: 0.75rem;
}

/* ==========================================================================
   Typography
   ========================================================================== */

h1, h2, h3, h4, h5, h6 {
    margin: 0 0 var(--vp-space-md) 0;
    font-weight: 600;
    line-height: var(--vp-line-height-tight);
    color: var(--vp-text-primary);
}

h1 { font-size: var(--vp-font-size-4xl); }
h2 { font-size: var(--vp-font-size-3xl); }
h3 { font-size: var(--vp-font-size-2xl); }
h4 { font-size: var(--vp-font-size-xl); }
h5 { font-size: var(--vp-font-size-lg); }
h6 { font-size: var(--vp-font-size-base); }

p {
    margin: 0 0 var(--vp-space-md) 0;
    line-height: var(--vp-line-height-normal);
}

a {
    color: var(--vp-primary);
    text-decoration: none;
    transition: color var(--vp-transition-fast);
}

a:hover,
a:focus {
    color: var(--vp-primary-dark);
    text-decoration: underline;
}

/* ==========================================================================
   Layout Components
   ========================================================================== */

.vp-container {
    width: 100%;
    max-width: var(--vp-container-xl);
    margin: 0 auto;
    padding: 0 var(--vp-space-md);
}

.vp-grid {
    display: grid;
    gap: var(--vp-space-lg);
}

.vp-flex {
    display: flex;
    gap: var(--vp-space-md);
}

/* ==========================================================================
   Responsive Breakpoints
   ========================================================================== */

@media (min-width: 640px) {
    .vp-container {
        padding: 0 var(--vp-space-lg);
    }
}

@media (min-width: 768px) {
    :root {
        --vp-font-size-4xl: 3rem;
        --vp-font-size-3xl: 2.25rem;
    }
}

@media (min-width: 1024px) {
    .vp-container {
        padding: 0 var(--vp-space-xl);
    }
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

.vp-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.vp-text-center { text-align: center; }
.vp-text-left { text-align: left; }
.vp-text-right { text-align: right; }

.vp-hidden { display: none; }
.vp-block { display: block; }
.vp-inline-block { display: inline-block; }

/* ==========================================================================
   WordPress Core Styles
   ========================================================================== */

.alignleft {
    float: left;
    margin: 0 var(--vp-space-lg) var(--vp-space-md) 0;
}

.alignright {
    float: right;
    margin: 0 0 var(--vp-space-md) var(--vp-space-lg);
}

.aligncenter {
    display: block;
    margin: 0 auto var(--vp-space-md);
    text-align: center;
}

.wp-caption {
    max-width: 100%;
    margin-bottom: var(--vp-space-md);
}

.wp-caption img {
    display: block;
    margin: 0 auto;
    max-width: 100%;
    height: auto;
}

.wp-caption-text {
    font-size: var(--vp-font-size-sm);
    color: var(--vp-text-secondary);
    text-align: center;
    margin-top: var(--vp-space-sm);
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    *,
    *::before,
    *::after {
        background: transparent !important;
        color: #000 !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }

    a,
    a:visited {
        text-decoration: underline;
    }

    img {
        page-break-inside: avoid;
    }

    h2,
    h3,
    p {
        orphans: 3;
        widows: 3;
    }

    h2,
    h3 {
        page-break-after: avoid;
    }
}