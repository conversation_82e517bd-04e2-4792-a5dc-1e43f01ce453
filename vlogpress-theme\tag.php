<?php
/**
 * The template for displaying tag archives
 *
 * @package VlogPress
 * @since 1.0.0
 */

get_header();
?>

<main id="main" class="site-main" role="main">
    <div class="vp-container">

        <?php if ( have_posts() ) : ?>

            <header class="page-header">
                <h1 class="page-title">
                    <?php
                    printf( esc_html__( 'Tag: %s', 'vlogpress' ), '<span>' . single_tag_title( '', false ) . '</span>' );
                    ?>
                </h1>

                <?php
                $tag_description = tag_description();
                if ( ! empty( $tag_description ) ) :
                    echo '<div class="archive-description">' . $tag_description . '</div>';
                endif;
                ?>
            </header><!-- .page-header -->

            <div class="vp-content-grid">
                <?php while ( have_posts() ) : the_post(); ?>

                    <article id="post-<?php the_ID(); ?>" <?php post_class( 'vp-content-card' ); ?>>

                        <?php if ( has_post_thumbnail() ) : ?>
                            <div class="vp-card-thumbnail">
                                <a href="<?php the_permalink(); ?>" aria-hidden="true" tabindex="-1">
                                    <?php the_post_thumbnail( 'medium_large' ); ?>
                                </a>

                                <?php if ( get_post_type() === 'vlog' ) : ?>
                                    <div class="vp-video-overlay">
                                        <svg class="vp-play-icon" width="48" height="48" viewBox="0 0 24 24" fill="none">
                                            <path d="M8 5V19L19 12L8 5Z" fill="currentColor"/>
                                        </svg>
                                    </div>

                                    <?php
                                    $video_duration = get_post_meta( get_the_ID(), '_vp_video_duration', true );
                                    if ( $video_duration ) : ?>
                                        <div class="vp-video-duration"><?php echo esc_html( $video_duration ); ?></div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>

                        <div class="vp-card-content">
                            <header class="vp-card-header">
                                <?php the_title( '<h2 class="vp-card-title"><a href="' . esc_url( get_permalink() ) . '" rel="bookmark">', '</a></h2>' ); ?>

                                <div class="vp-card-meta">
                                    <span class="vp-content-type <?php echo get_post_type() === 'vlog' ? 'vp-type-video' : 'vp-type-blog'; ?>">
                                        <?php echo get_post_type() === 'vlog' ? __( 'Video', 'vlogpress' ) : __( 'Blog', 'vlogpress' ); ?>
                                    </span>

                                    <time class="vp-published-date" datetime="<?php echo esc_attr( get_the_date( 'c' ) ); ?>">
                                        <?php echo esc_html( get_the_date() ); ?>
                                    </time>
                                </div>
                            </header>

                            <div class="vp-card-excerpt">
                                <?php the_excerpt(); ?>
                            </div>

                            <footer class="vp-card-footer">
                                <a href="<?php the_permalink(); ?>" class="vp-read-more">
                                    <?php echo get_post_type() === 'vlog' ? __( 'Watch Video', 'vlogpress' ) : __( 'Read More', 'vlogpress' ); ?>
                                </a>
                            </footer>
                        </div>
                    </article>

                <?php endwhile; ?>
            </div>

            <?php
            the_posts_pagination( array(
                'mid_size'  => 2,
                'prev_text' => __( '&larr; Previous', 'vlogpress' ),
                'next_text' => __( 'Next &rarr;', 'vlogpress' ),
            ) );
            ?>

        <?php else : ?>

            <section class="vp-no-results">
                <header class="page-header">
                    <h1 class="page-title"><?php _e( 'Nothing here', 'vlogpress' ); ?></h1>
                </header>

                <div class="page-content">
                    <p><?php _e( 'It seems we can&rsquo;t find what you&rsquo;re looking for with this tag. Perhaps searching can help.', 'vlogpress' ); ?></p>
                    <?php get_search_form(); ?>
                </div>
            </section>

        <?php endif; ?>

    </div>
</main>

<?php
get_sidebar();
get_footer();